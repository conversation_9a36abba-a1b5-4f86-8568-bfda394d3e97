import os
import argparse
import json
import torch
import random
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from copy import deepcopy

from model.gat import GATConv 

class GradWhere(torch.autograd.Function):
    """
    We can implement our own custom autograd Functions by subclassing
    torch.autograd.Function and implementing the forward and backward passes
    which operate on Tensors.
    """

    @staticmethod
    def forward(ctx, input, thrd, device):
        """
        In the forward pass we receive a Tensor containing the input and return
        a Tensor containing the output. ctx is a context object that can be used
        to stash information for backward computation. You can cache arbitrary
        objects for use in the backward pass using the ctx.save_for_backward method.
        """
        ctx.save_for_backward(input)
        rst = torch.where(input>thrd, torch.tensor(1.0, device=device, requires_grad=True),
                                      torch.tensor(0.0, device=device, requires_grad=True))
        return rst

    @staticmethod
    def backward(ctx, grad_output):
        """
        In the backward pass we receive a Tensor containing the gradient of the loss
        with respect to the output, and we need to compute the gradient of the loss
        with respect to the input.
        """
        input, = ctx.saved_tensors
        grad_input = grad_output.clone()
        
        """
        Return results number should corresponding with .forward inputs (besides ctx),
        for each input, return a corresponding backward grad
        """
        return grad_input, None, None

# class TriggerGenerator(nn.Module):
#     '''GW硬化'''
#     def __init__(self, node_feat_dim, edge_feat_dim, trigger_shape):
#         super().__init__()
#         self.gat1 = GATConv(node_feat_dim, edge_feat_dim, 32, n_heads=1, generator=True)
#         self.gat2 = GATConv(32, edge_feat_dim, 64, n_heads=1, generator=True)
#         self.mlp = nn.Sequential(
#             nn.Linear(64, 128),
#             nn.LayerNorm(128), # 新增
#             nn.ReLU(),
#             nn.Linear(128, int(np.prod(trigger_shape)))
#         )
#         self.trigger_shape = trigger_shape
#         self.GW = GradWhere.apply
        
#     def forward(self, subgraph, target_node_idx, temperature=0.5, hard=True, test=False):
#         h = subgraph.ndata['attr']
#         h = self.gat1(subgraph, h)
#         h = self.gat2(subgraph, h)
#         trigger_emb = h[target_node_idx]  # .mean(dim=0)
#         trigger = torch.sigmoid(self.mlp(trigger_emb))
#         trigger = self.GW(trigger, 0.5, 'cuda')
#         return trigger.view(-1, *self.trigger_shape)

class TriggerGenerator(nn.Module):
    '''Gumbel-Softmax'''
    def __init__(self, node_feat_dim, edge_feat_dim, trigger_shape):
        super().__init__()
        self.gat1 = GATConv(node_feat_dim, edge_feat_dim, 32, n_heads=1, generator=True)
        self.gat2 = GATConv(32, edge_feat_dim, 64, n_heads=1, generator=True)
        self.mlp = nn.Sequential(
            nn.Linear(64, 128),
            nn.LayerNorm(128), # 新增
            nn.ReLU(),
            nn.Linear(128, int(np.prod(trigger_shape)))
        )
        self.trigger_shape = trigger_shape
        
    def forward(self, subgraph, target_node_idx, temperature=0.5, hard=True, test=False):
        h = subgraph.ndata['attr']
        h = self.gat1(subgraph, h)
        h = self.gat2(subgraph, h)
        trigger_emb = h[target_node_idx]  # .mean(dim=0)
        logits = self.mlp(trigger_emb)
        if test:
            trigger = (logits > 0).float()
        else:
            logits_paired = torch.stack([logits, torch.zeros_like(logits)], dim=-1)
            edge_existence_prob = F.gumbel_softmax(logits_paired, tau=temperature, hard=hard, dim=-1)
            trigger = edge_existence_prob[..., 0] 
        return trigger.view(-1, *self.trigger_shape)
    
def get_trigger_index(trigger_size):
	# 用触发器size初始化edge_index,进程索引为1，2，3...，文件索引为process_num+1，process_num+2...
	# edge_index的顺序必须与触发器一致
	u = []
	v = []
	process_num = trigger_size[0]-1  # 子进程数量
	file_idx = torch.arange(trigger_size[1])+process_num+1
 
	for i in range(process_num):# fork    1*process_num
		process_idx = i+1  # 从1开始
		u.append(0)
		v.append(process_idx)
  
	for f in file_idx:  # 2*file_num
		u.append(0)
		v.append(f.item())
		u.append(f.item())
		v.append(0)
  
	for i in range(process_num):
		process_idx = i+1
		for j in file_idx:
			u.append(process_idx)
			v.append(j.item())
			u.append(j.item())
			v.append(process_idx)
	return (u,v)  # 总边数：2*file_num+process_num+process_num*file_num*2 = 2*file_num*trigger_size[0]+process_num

def get_trojan_edge(start, idx, edge_index, trigger_size):
	'''
	========为edge_index重赋索引=========
	start: 当前最大节点索引
	idx: 要添加触发器的目标节点索引
	edge_index: 触发器模板edge_index
	'''
	(u_orig, v_orig) = edge_index
	u = u_orig.copy()
	v = v_orig.copy()
			
	for i in range(len(u)):
		if u[i] == 0:
			u[i] = idx
		else:
			u[i] = u[i] + start
	for i in range(len(v)):
		if v[i] == 0:
			v[i] = idx
		else:
			v[i] = v[i] + start

	return (u, v), start+trigger_size[0]-1+trigger_size[1]

# class AddTrigger(nn.Module):
# 	def __init__(self, cfg):
# 		super(AddTrigger, self).__init__()
# 		self.config = cfg
# 		self.size = 15
# 		self.trigger_edge_index = get_trigger_index(self.config.trigger_shape)  # 触发器模板
# 		if cfg.dataset == 'theia':
# 			self.file_type_name = 'FILE_OBJECT_BLOCK'
# 			self.clone_or_fork_name = 'EVENT_CLONE'
# 		elif cfg.dataset == 'cadets':
# 			self.file_type_name = 'FILE_OBJECT_FILE'
# 			self.clone_or_fork_name = 'EVENT_FORK'
      
		
# 	def forward(self, orig_data, target_node_idx, socket_msg_all, file_msg_all, trigger, edge_weight=True):

# 		for socket in sockets:
# 			tmp_file_type = torch.tensor([self.config.node_type_dict[self.file_type_name]], device=self.config.device).repeat(self.size)
# 			tmp_file_attr = F.one_hot(torch.tensor(self.config.node_type_dict[self.file_type_name], device=self.config.device), self.config.n_dim).float().repeat(self.size, 1)
# 			u_add = torch.arange(orig_data.num_nodes(), orig_data.num_nodes()+self.size, device=self.config.device)
# 			v_add = torch.tensor([socket]*self.size, device=self.config.device)
# 			edge_type_add = torch.tensor([self.config.edge_type_dict['EVENT_READ']]*self.size, device=self.config.device)
# 			orig_data.add_nodes(self.size, data={
# 				'attr': tmp_file_attr,
# 				'type': tmp_file_type,
# 			})
# 			orig_data.add_edges(u_add, v_add, data={
# 				'attr': F.one_hot(edge_type_add, self.config.e_dim).float(),
# 				'type': edge_type_add,
# 			})
# 		return orig_data


class AddTrigger(nn.Module):
	def __init__(self, cfg):
		super(AddTrigger, self).__init__()
		self.config = cfg
		self.trigger_edge_index = get_trigger_index(self.config.trigger_shape)  # 触发器模板
		if cfg.dataset == 'theia':
			self.file_type_name = 'FILE_OBJECT_BLOCK'
			self.clone_or_fork_name = 'EVENT_CLONE'
		elif cfg.dataset == 'cadets':
			self.file_type_name = 'FILE_OBJECT_FILE'
			self.clone_or_fork_name = 'EVENT_FORK'
      
		
	def forward(self, orig_data, target_node_idx, socket_msg_all, file_msg_all, trigger, edge_weight):
		start = orig_data.num_nodes() - 1  # 当前最大节点索引

		# 新加的节点和边的数量
		num_targets = len(target_node_idx)
		num_new_nodes = num_targets * (self.config.trigger_shape[0]-1 + self.config.trigger_shape[1])
		num_new_edges = num_targets * len(self.trigger_edge_index[0])

		# 占位
		x_add_all = torch.zeros((num_new_nodes, self.config.n_dim), device=self.config.device)  # 'attr'
		x_type_add_all = torch.zeros((num_new_nodes), dtype=torch.int64, device=self.config.device)  # 'type'
		u_all = torch.zeros((num_new_edges), dtype=torch.long, device=self.config.device)
		v_all = torch.zeros((num_new_edges), dtype=torch.long, device=self.config.device)
		edge_type_all = torch.zeros((num_new_edges), dtype=torch.int64, device=self.config.device)  # edge_type
		edge_weights_all = torch.zeros(num_new_edges, device=self.config.device)

		# ========其他类型节点的连边========   不确定数目、且不优化，全连接
		u1_all = []
		v1_all = []
		edge_type1_all = []
		# ======================

		process_num = self.config.trigger_shape[0]-1  # 子进程数目

		# 批处理优化：预先计算固定的节点特征和边类型
		# 进程节点特征 (批量生成)
		x_process_template = F.one_hot(torch.tensor(self.config.node_type_dict['SUBJECT_PROCESS'], device=self.config.device), self.config.n_dim).float()
		x_type_process_template = torch.tensor([self.config.node_type_dict['SUBJECT_PROCESS']], device=self.config.device)

		# 文件节点特征 (批量生成)
		x_file_template = F.one_hot(torch.tensor(self.config.node_type_dict[self.file_type_name], device=self.config.device), self.config.n_dim).float()
		x_type_file_template = torch.tensor([self.config.node_type_dict[self.file_type_name]], device=self.config.device)

		# 边类型模板 (批量生成)
		edge_type_p_c_template = torch.tensor([self.config.edge_type_dict[self.clone_or_fork_name]], device=self.config.device).repeat(process_num)
		edge_type_p_f_template = torch.tensor([self.config.edge_type_dict['EVENT_OPEN'], self.config.edge_type_dict['EVENT_READ']], device=self.config.device).repeat(self.config.trigger_shape[1])
		edge_type_c_f_template = torch.tensor([self.config.edge_type_dict['EVENT_OPEN'], self.config.edge_type_dict['EVENT_READ']], device=self.config.device).repeat(process_num*self.config.trigger_shape[1])
		edge_type_template = torch.cat([edge_type_p_c_template, edge_type_p_f_template, edge_type_c_f_template], dim=0)

		# 批处理计算触发器边权重
		trigger_flattened = trigger.view(num_targets, -1)  # [num_targets, trigger_flat_size]
		process_ones = torch.ones(num_targets, process_num, device=self.config.device)  # [num_targets, process_num]
		edge_scores_batch = torch.cat([
			process_ones,
			trigger_flattened.repeat_interleave(2, dim=1)
		], dim=1)  # [num_targets, total_edges_per_target]

		# 批量生成所有节点特征
		nodes_per_target = process_num + self.config.trigger_shape[1]
		edges_per_target = len(self.trigger_edge_index[0])

		# 批量填充节点特征
		for idx in range(num_targets):
			node_start = idx * nodes_per_target
			# 进程节点
			x_add_all[node_start:node_start+process_num] = x_process_template.repeat(process_num, 1)
			x_type_add_all[node_start:node_start+process_num] = x_type_process_template.repeat(process_num)
			# 文件节点
			x_add_all[node_start+process_num:node_start+nodes_per_target] = x_file_template.repeat(self.config.trigger_shape[1], 1)
			x_type_add_all[node_start+process_num:node_start+nodes_per_target] = x_type_file_template.repeat(self.config.trigger_shape[1])

		# 批量填充边权重和边类型
		for idx in range(num_targets):
			edge_start = idx * edges_per_target
			edge_weights_all[edge_start:edge_start+edges_per_target] = edge_scores_batch[idx]
			edge_type_all[edge_start:edge_start+edges_per_target] = edge_type_template

		for idx, node in enumerate(target_node_idx):
			# 生成新边 (这部分仍需要循环，因为依赖于具体的节点索引)
			(u,v), start_new = get_trojan_edge(start, node, self.trigger_edge_index, self.config.trigger_shape)
			edge_start = idx * edges_per_target
			u_all[edge_start:edge_start+len(u)] = torch.tensor(u, dtype=torch.long, device=self.config.device)
			v_all[edge_start:edge_start+len(v)] = torch.tensor(v, dtype=torch.long, device=self.config.device)
			
			##################其他类型的节点 ############
			if node in socket_msg_all:
				socket_list = socket_msg_all[node]
				if len(socket_list)>0:
					count = 0
					for socket in socket_list:
						count += 1
						u1_all.extend([c_process for c_process in range(start+1, start+1+process_num)])
						v1_all.extend([socket for _ in range(process_num)])  # EVENT_CONNECT
					edge_type1_all.append(torch.tensor([self.config.edge_type_dict['EVENT_CONNECT']], device=self.config.device).repeat(process_num*count))

			if node in file_msg_all:
				file_list = file_msg_all[node]  # list
				if len(file_list)>0:
					count = 0
					for file in file_list:
						count += 1
						u1_all.extend([c_process for c_process in range(start+1, start+1+process_num)])
						v1_all.extend([file for _ in range(process_num)])
					edge_type1_all.append(torch.tensor([self.config.edge_type_dict['EVENT_OPEN']], device=self.config.device).repeat(process_num*count))
			#################其他类型的节点#############
			
			start = start_new

		# 验证批处理结果
		assert num_new_nodes == num_targets * nodes_per_target
		assert num_new_edges == num_targets * edges_per_target

		u1_all = torch.tensor(u1_all, dtype=torch.long, device=self.config.device)
		v1_all = torch.tensor(v1_all, dtype=torch.long, device=self.config.device)
		edge_type1_all = torch.cat(edge_type1_all, dim=0) if edge_type1_all else torch.empty(0, dtype=torch.int64, device=self.config.device)
		edge_weights1_all = torch.ones(len(u1_all), device=self.config.device)  # 全1  不优化

		u_all = torch.cat([u_all, u1_all], dim=0)
		v_all = torch.cat([v_all, v1_all], dim=0)
		edge_type_all = torch.cat([edge_type_all, edge_type1_all], dim=0)
		edge_weights_all = torch.cat([edge_weights_all, edge_weights1_all], dim=0)
	
		if edge_weight:  # 优化触发器时   软化权重
			orig_data.add_nodes(len(x_add_all), data={
			'attr':x_add_all,
			'type': x_type_add_all,
			})
   
			orig_data.add_edges(u_all, v_all, data={
				'attr': F.one_hot(edge_type_all, self.config.e_dim).float(),
				'type': edge_type_all,
				'edge_weights': edge_weights_all
			})
		else:  # 优化检测器时 or 正式攻击时    硬化
			valid_edge_mask = edge_weights_all.bool()  # 硬化
			# valid_edge_mask = (edge_weights_all>0.5).bool()  # 软化
			if not torch.any(valid_edge_mask):
				return orig_data

			valid_u_all = u_all[valid_edge_mask]
			valid_v_all = v_all[valid_edge_mask]
			valid_edge_type_all = edge_type_all[valid_edge_mask]
			
			# 去掉孤立节点
			original_graph_num_nodes = orig_data.num_nodes()
			nodes_in_valid_edges = torch.unique(torch.cat([valid_u_all, valid_v_all]))
			valid_new_node_temp_indices_mask = nodes_in_valid_edges >= original_graph_num_nodes
			valid_new_node_temp_indices = nodes_in_valid_edges[valid_new_node_temp_indices_mask]
			num_valid_new_nodes = len(valid_new_node_temp_indices)
			max_temp_index = torch.max(u_all) # u_all 包含所有新旧节点的最大索引
			if v_all.numel() > 0:
				max_temp_index = max(max_temp_index, torch.max(v_all))
			remap_tensor = torch.full((max_temp_index + 1,), -1, dtype=torch.long, device=self.config.device)
			original_node_indices = torch.arange(original_graph_num_nodes, device=self.config.device)
			remap_tensor[original_node_indices] = original_node_indices
			final_new_node_indices = torch.arange(
				original_graph_num_nodes, 
				original_graph_num_nodes + num_valid_new_nodes,
				device=self.config.device
			)
			remap_tensor[valid_new_node_temp_indices] = final_new_node_indices
			remapped_u = remap_tensor[valid_u_all]
			remapped_v = remap_tensor[valid_v_all]
			indices_in_x_add = valid_new_node_temp_indices - original_graph_num_nodes
			valid_x_add = x_add_all[indices_in_x_add]
			valid_x_type_add = x_type_add_all[indices_in_x_add]
   
			orig_data.add_nodes(num_valid_new_nodes, data={
				'attr': valid_x_add,
				'type': valid_x_type_add,
			})
			orig_data.add_edges(remapped_u, remapped_v, data={
				'attr': F.one_hot(valid_edge_type_all, self.config.e_dim).float(),
				'type': valid_edge_type_all,
			})
		return orig_data

		# 老版本，未去掉孤立节点
		# 	orig_data.add_nodes(len(x_add_all), data={
		# 	'attr':x_add_all,
		# 	'type': x_type_add_all,
		# 	})
		# 	# ==========硬化优化==========
		# 	valid_edge_mask = edge_weights_all.bool()
		# 	# # =========硬化==========
   
		# 	valid_u_all = u_all[valid_edge_mask]
		# 	valid_v_all = v_all[valid_edge_mask]
		# 	valid_edge_type_all = edge_type_all[valid_edge_mask]
		# 	# print(f'all add edges num:{len(valid_u_all)}')
		# 	if len(valid_u_all) > 0:
		# 		orig_data.add_edges(valid_u_all, valid_v_all, data={
		# 			'attr': F.one_hot(valid_edge_type_all, self.config.e_dim).float(),
		# 			'type': valid_edge_type_all,
		# 		})
		# 	else:
		# 		pass

		# return orig_data

def get_mal_node_msg(cfg, g):
	'''
	获取恶意节点,返回字典{label: [idx1, idx2, ...]}、恶意文件连接信息
	'''
	dataset = cfg.dataset

	# malicious node
	malicious_node = {}
	with open(f'./data/{dataset}/test_node_map.json', 'r') as f:
		test_node_map = json.load(f)  # uuid -> idx
	with open(f'./data/{dataset}/types.json', 'r') as f:
		type_dict = json.load(f)  # uuid -> type

	false_count = 0
	with open(f'./data/{dataset}/{dataset}.txt', 'r') as f:
		for line in f:
			uuid = line.strip('\n')
			try:
				idx = test_node_map[uuid]
				label = type_dict[uuid]
				if label not in malicious_node:
					malicious_node[label] = []
				malicious_node[label].append(idx)
			except:
				false_count += 1
				pass
	print(f'false_mal_count: {false_count}')
	
	# 每个恶意文件节点是否只添加一次？
	mal_file_msg = {}  # list
	mal_file_count = 0
	# have_done = set()
	for mal_process_node in malicious_node['SUBJECT_PROCESS']:
		# neighbors = torch.unique(torch.cat([g.successors(mal_process_node), g.predecessors(mal_process_node)], dim=0))  # 邻居
		neighbors = g.successors(mal_process_node)  # 邻居
		file_mask = (g.ndata['type'][neighbors]==cfg.node_type_dict['FILE_OBJECT_BLOCK']).nonzero().squeeze()
		mask = file_mask.tolist() if file_mask.numel()>0 else []
		if isinstance(mask, int):
			mask = [mask]
		mal_file_msg[mal_process_node] = [neighbors[mask_i].item() for mask_i in mask if neighbors[mask_i].item() in malicious_node['FILE_OBJECT_BLOCK']]
		mal_file_count += len(mal_file_msg[mal_process_node])
		# mal_file_msg[mal_process_node] = [neighbors[mask_i].item() for mask_i in mask if (neighbors[mask].item() in malicious_node['FILE_OBJECT_BLOCK']) and neighbors[mask].item() not in have_done]
		# for i in mal_file_msg[mal_process_node]:
		# 	have_done.add(i)
		# have_done.add()
	print(f'mal_file_count:{mal_file_count}')

	mal_socket_msg = {}
	for mal_process_node in malicious_node['SUBJECT_PROCESS']:
		# neighbors = torch.unique(torch.cat([g.successors(mal_process_node), g.predecessors(mal_process_node)], dim=0))  # 邻居
		neighbors = g.successors(mal_process_node)  # 邻居
		file_mask = (g.ndata['type'][neighbors]==cfg.node_type_dict['NetFlowObject']).nonzero().squeeze()
		mask = file_mask.tolist() if file_mask.numel()>0 else []
		if isinstance(mask, int):
			mask = [mask]
		mal_socket_msg[mal_process_node] = []
		for i in mask:
			if neighbors[i].item() in malicious_node['NetFlowObject']:
				mal_socket_msg[mal_process_node].append(neighbors[i].item())
	return malicious_node, mal_file_msg, mal_socket_msg

def get_map(dataset):
	# 标签映射
	with open(f'./data/{dataset}/node_type_dict.json', 'r') as f:
		node_type_dict = json.load(f)
	with open(f'./data/{dataset}/edge_type_dict.json', 'r') as f:
		edge_type_dict = json.load(f)
	return node_type_dict, edge_type_dict

def get_node_map(dataset):
	with open(f'./data/{dataset}/train_node_map.json', 'r') as f:
		train_node_map = json.load(f)
	with open(f'./data/{dataset}/test_node_map.json', 'r') as f:
		test_node_map = json.load(f)
	return train_node_map, test_node_map

# def choose_poisonable_nodes(dataset, ratio, train_data):
# 	# if not new:
# 	# 	print('已选用固定投毒节点')
# 	# 	with open(f"./data/{dataset}/choosed_poisoned_nodes_uuid_{ratio}.json", "r") as f:
# 	# 		choosed_poisoned_nodes_uuid = json.load(f)
# 	# 	choosed_nodes_idx_all = {}
# 	# 	for key, value in choosed_poisoned_nodes_uuid.items():
# 	# 		key = int(key)
# 	# 		value = [train_node_map[key][i] for i in value]
# 	# 		choosed_nodes_idx_all[key] = value
# 	# 	return choosed_nodes_idx_all

# 	if isinstance(train_data, list):
# 		choosed_nodes_idx_all = {}

# 			print(f"训练集{i} 投毒节点数量: {len(choosed_nodes_uuid)}，投毒比例: {len(choosed_nodes_uuid)/process_num}")
   
# 		with open(f"./data/{dataset}/choosed_poisoned_nodes_uuid_{ratio}.json", "w") as f:
# 			json.dump(choosed_nodes_uuid_all, f)

# 	else:
# 		raise NotImplementedError
    
# 	return choosed_nodes_idx_all
