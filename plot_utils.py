from sklearn.decomposition import PCA
import matplotlib
matplotlib.use('Agg')  # 设置非交互式后端，避免tkinter线程错误
import matplotlib.pyplot as plt
import torch
import numpy as np
import umap
from sklearn.preprocessing import StandardScaler
import os
from sklearn.manifold import TSNE
import matplotlib.colors as mcolors
from typing import Dict, List, Optional, Union, Tuple
import seaborn as sns
import warnings

warnings.filterwarnings("ignore", category=UserWarning)

def viz_eval_thre(y_test, score, label_map):
    y_test = y_test.astype(int)
    plt.figure(figsize=(10, 8))

    sns.stripplot(x=y_test, y=score, hue=label_map, jitter=0.5, alpha=0.5, palette="Set1")
    
    ##
    highlight_x = y_test[207781]
    highlight_y = score[207781]
    # 用不同的颜色和大小绘制这个点
    plt.scatter(x=highlight_x, y=highlight_y, 
                color='gold', s=200,  # 金色，更大的点
                edgecolor='black', linewidth=2,  # 黑色边框
                label='Highlighted Point', zorder=5)  # zorder确保在最上层
    ##
    
    plt.ylim(0, 3000)  # 强制 y 轴范围从 0 到 3000
    plt.yticks(np.concatenate([[-200], np.arange(0, 3000, 500)]))
    plt.xticks([0, 1], ['Normal', 'Anomaly'])
    
    # 添加图例，显示类别标签
    plt.legend(title="Class", bbox_to_anchor=(1.05, 1), loc='upper left')
    
    plt.tight_layout()  # 防止标签重叠
    # 保存图像而不是显示
    os.makedirs('./eval_results', exist_ok=True)
    plt.savefig('./eval_results/eval_threshold_plot.png', bbox_inches='tight', dpi=300)
    print("图像已保存到: ./eval_results/eval_threshold_plot.png")
    plt.close()  # 关闭图形以释放内存
    
def plot_embeddings2(train_hidden_feature, x_test):
    # 合并训练和测试数据
    all_embeddings = torch.cat([train_hidden_feature, x_test], dim=0).numpy()
    labels = np.array([0] * len(train_hidden_feature) + [1] * len(x_test))  # 0: train, 1: test

    # PCA降维到2D
    pca = PCA(n_components=2)
    embeddings_2d = pca.fit_transform(all_embeddings)

    # 可视化
    plt.figure(figsize=(10, 6))
    plt.scatter(embeddings_2d[labels == 0, 0], embeddings_2d[labels == 0, 1], 
                c='blue', alpha=0.5, s=2, label='Train (52,887 samples)')
    plt.scatter(embeddings_2d[labels == 1, 0], embeddings_2d[labels == 1, 1], 
                c='red', alpha=0.8, marker='X', s=100, label='Test (23 samples)')
    plt.title('PCA Projection of Embeddings')
    plt.legend()
    plt.show()

def plot_embeddings_local(train_hidden_feature, x_test, epoch, choosed_poisoned_nodes, n_std=10):
    # 合并数据并降维
    all_embeddings = torch.cat([train_hidden_feature, x_test], dim=0).numpy()
    labels = np.array([0] * len(train_hidden_feature) + [1] * len(x_test))
    
    pca = PCA(n_components=2)
    embeddings_2d = pca.fit_transform(all_embeddings)
    
    # 提取测试点坐标
    test_points = embeddings_2d[labels == 1]
    test_mean = test_points.mean(axis=0)  # 测试点中心
    test_std = test_points.std(axis=0)    # 测试点标准差
    
    # 计算筛选边界
    x_min, x_max = test_mean[0] - n_std * test_std[0], test_mean[0] + n_std * test_std[0]
    y_min, y_max = test_mean[1] - n_std * test_std[1], test_mean[1] + n_std * test_std[1]
    
    # 筛选训练点（仅保留边界内的点）
    train_mask = (
        (embeddings_2d[labels == 0, 0] >= x_min) & 
        (embeddings_2d[labels == 0, 0] <= x_max) & 
        (embeddings_2d[labels == 0, 1] >= y_min) & 
        (embeddings_2d[labels == 0, 1] <= y_max)
    )
    poisoned_mask = np.zeros(len(train_hidden_feature), dtype=bool)
    for _,value in choosed_poisoned_nodes.items():
        poisoned_mask[value] = True
    
    filtered_train_clean = embeddings_2d[labels == 0][train_mask & ~poisoned_mask]
    filtered_train_poisoned = embeddings_2d[labels == 0][train_mask & poisoned_mask]
    
    # 可视化局部区域
    plt.figure(figsize=(10, 6))
    plt.scatter(
        filtered_train_clean[:, 0], filtered_train_clean[:, 1], 
        c='blue', alpha=0.5, s=10, 
        label=f'Train clean (filtered: {len(filtered_train_clean)}/{len(train_hidden_feature)})'
    )
    plt.scatter(
        filtered_train_poisoned[:, 0], filtered_train_poisoned[:, 1], 
        c='red', alpha=0.8, s=40, 
        label=f'Train poisoned (filtered: {len(filtered_train_poisoned)}/{len(train_hidden_feature)})',
        edgecolors='black', linewidth=1.5, zorder=2  # 边框
    )
    plt.scatter(
        test_points[:, 0], test_points[:, 1], 
        c='red', alpha=0.8, marker='X', s=30, 
        label=f'Test ({len(x_test)} samples)'
    )
    
    # 标记测试区域边界
    plt.plot(
        [x_min, x_max, x_max, x_min, x_min],
        [y_min, y_min, y_max, y_max, y_min],
        'k--', alpha=0.3, label=f'Test Region (±{n_std}σ)'
    )
    
    plt.title('PCA Projection (Local Region Near Test Points)')
    plt.legend()
    # plt.show()
    if not os.path.exists('./poison_model/embedding/local'):
        os.makedirs('./poison_model/embedding/local')
    filename = f"embedding_local_epoch{epoch}.png" if epoch is not None else f"embedding_local.png"
    save_path = os.path.join('./poison_model/embedding/local', filename)
    plt.savefig(save_path, bbox_inches='tight', dpi=150)
    plt.close()

def plot_embeddings_global(train_hidden_feature, x_test, epoch, choosed_poisoned_nodes, n_std=10):
    # 合并数据并降维
    all_embeddings = torch.cat([train_hidden_feature, x_test], dim=0).numpy()
    labels = np.array([0] * len(train_hidden_feature) + [1] * len(x_test))
    
    pca = PCA(n_components=2)
    embeddings_2d = pca.fit_transform(all_embeddings)
    
    # 提取测试点坐标
    test_points = embeddings_2d[labels == 1]

    poisoned_mask = np.zeros(len(train_hidden_feature), dtype=bool)
    for _,value in choosed_poisoned_nodes.items():
        poisoned_mask[value] = True
    
    filtered_train_clean = embeddings_2d[labels == 0][~poisoned_mask]
    filtered_train_poisoned = embeddings_2d[labels == 0][poisoned_mask]
    
    plt.figure(figsize=(10, 6))
    plt.scatter(
        filtered_train_clean[:, 0], filtered_train_clean[:, 1], 
        c='blue', alpha=0.5, s=10, 
        label=f'Train clean: {len(filtered_train_clean)}/{len(train_hidden_feature)}'
    )
    plt.scatter(
        filtered_train_poisoned[:, 0], filtered_train_poisoned[:, 1], 
        c='red', alpha=0.8, s=40, 
        label=f'Train poisoned: {len(filtered_train_poisoned)}/{len(train_hidden_feature)}',
        edgecolors='black', linewidth=1.5, zorder=2  # 边框
    )
    plt.scatter(
        test_points[:, 0], test_points[:, 1], 
        c='red', alpha=0.8, marker='X', s=30, 
        label=f'Test ({len(x_test)} samples)'
    )
    plt.title('PCA Projection (Global Region)')
    plt.legend()
    # plt.show()
    filename = f"embedding_global_epoch{epoch}.png" if epoch is not None else f"embedding_global.png"
    if not os.path.exists('./poison_model/embedding/global'):
        os.makedirs('./poison_model/embedding/global')
    save_path = os.path.join('./poison_model/embedding/global', filename)
    plt.savefig(save_path, bbox_inches='tight', dpi=150)
    plt.close()

def plot_embeddings_local_umap(train_hidden_feature, x_test, epoch, choosed_poisoned_nodes, n_std=10):
    """
    使用UMAP降维可视化隐藏特征（局部区域）
    
    参数:
        train_hidden_feature: 训练集隐藏特征 (Tensor)
        x_test: 测试集隐藏特征 (Tensor)
        epoch: 当前epoch（用于文件名）
        choosed_poisoned_nodes: 投毒节点字典 {index: node_id}
        n_std: 测试点区域边界标准差倍数 (默认3σ)
    """
    # 合并数据并标准化
    all_embeddings = torch.cat([train_hidden_feature, x_test], dim=0).numpy()
    scaler = StandardScaler()
    all_embeddings = scaler.fit_transform(all_embeddings)  # UMAP对尺度敏感
    
    # UMAP降维
    reducer = umap.UMAP(n_components=2, random_state=42, n_neighbors=15, min_dist=0.1)
    embeddings_2d = reducer.fit_transform(all_embeddings)
    
    # 标签处理
    labels = np.array([0] * len(train_hidden_feature) + [1] * len(x_test))
    test_points = embeddings_2d[labels == 1]
    test_mean = test_points.mean(axis=0)
    test_std = test_points.std(axis=0)
    
    # 计算筛选边界
    x_min, x_max = test_mean[0] - n_std * test_std[0], test_mean[0] + n_std * test_std[0]
    y_min, y_max = test_mean[1] - n_std * test_std[1], test_mean[1] + n_std * test_std[1]
    
    # 生成掩码
    train_mask = (
        (embeddings_2d[labels == 0, 0] >= x_min) & 
        (embeddings_2d[labels == 0, 0] <= x_max) & 
        (embeddings_2d[labels == 0, 1] >= y_min) & 
        (embeddings_2d[labels == 0, 1] <= y_max)
    )
    
    poisoned_mask = np.zeros(len(train_hidden_feature), dtype=bool)
    for node_id in choosed_poisoned_nodes.values():
        poisoned_mask[node_id] = True
    
    # 筛选数据
    filtered_train_clean = embeddings_2d[labels == 0][train_mask & ~poisoned_mask]
    filtered_train_poisoned = embeddings_2d[labels == 0][train_mask & poisoned_mask]
    
    # 可视化
    plt.figure(figsize=(12, 8))
    
    # 绘制测试点区域背景
    plt.fill_between(
        [x_min, x_max], y_min, y_max,
        color='orange', alpha=0.05, label=f'Test Region (±{n_std}σ)'
    )
    
    # 绘制点集
    plt.scatter(
        filtered_train_clean[:, 0], filtered_train_clean[:, 1],
        c='blue', alpha=0.6, s=8, edgecolor='w', linewidth=0.3,
        label=f'Clean Train ({len(filtered_train_clean)}/{len(train_hidden_feature)})'
    )
    plt.scatter(
        filtered_train_poisoned[:, 0], filtered_train_poisoned[:, 1],
        c='red', alpha=0.8, s=12, marker='*', edgecolor='k', linewidth=0.5,
        label=f'Poisoned Train ({len(filtered_train_poisoned)})'
    )
    plt.scatter(
        test_points[:, 0], test_points[:, 1],
        c='lime', alpha=0.9, marker='X', s=50, edgecolor='k',
        label=f'Test ({len(x_test)})'
    )
    
    # 标注统计信息
    plt.text(
        0.05, 0.95,
        f'Poisoned/Clean Ratio: {len(filtered_train_poisoned)}/{len(filtered_train_clean)} = {len(filtered_train_poisoned)/len(filtered_train_clean):.1%}',
        transform=plt.gca().transAxes, ha='left', va='top',
        bbox=dict(facecolor='white', alpha=0.7)
    )
    
    # 美化图形
    plt.title(f'UMAP Projection (Epoch {epoch})' if epoch else 'UMAP Projection')
    plt.legend(loc='upper right', framealpha=0.7)
    plt.grid(True, alpha=0.2)
    
    # 保存结果
    os.makedirs('./poison_model/embedding/local_umap', exist_ok=True)
    filename = f"umap_local_epoch{epoch}.png" if epoch else "umap_local.png"
    plt.savefig(
        f'./poison_model/embedding/local_umap/{filename}',
        dpi=200, bbox_inches='tight', pad_inches=0.1
    )
    plt.close()
    
def plot_embeddings_tsne(train_hidden_feature, x_test, epoch, choosed_poisoned_nodes, n_std=100):
    # 合并数据并降维
    all_embeddings = torch.cat([train_hidden_feature, x_test], dim=0).numpy()
    labels = np.array([0] * len(train_hidden_feature) + [1] * len(x_test))
    
    # 使用 t-SNE 降维到 2D
    tsne = TSNE(n_components=2, random_state=42, perplexity=20, n_iter=1000)
    embeddings_2d = tsne.fit_transform(all_embeddings)
    
    # 提取测试点坐标
    test_points = embeddings_2d[labels == 1]
    test_mean = test_points.mean(axis=0)  # 测试点中心
    test_std = test_points.std(axis=0)    # 测试点标准差
    
    # 计算筛选边界
    x_min, x_max = min(test_points[:, 0]) - 3, max(test_points[:, 0]) + 3
    y_min, y_max = min(test_points[:, 1]) - 3, max(test_points[:, 1]) + 3
    
    poisoned_mask = np.zeros(len(train_hidden_feature), dtype=bool)
    for _, value in choosed_poisoned_nodes.items():
        poisoned_mask[value] = True
    
    filtered_train_clean = embeddings_2d[labels == 0][~poisoned_mask]
    filtered_train_poisoned = embeddings_2d[labels == 0][poisoned_mask]
    
    plt.figure(figsize=(10, 6))
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 使用微软雅黑
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    plt.xlim(x_min, x_max)  # x轴范围
    plt.ylim(y_min, y_max)  # y轴范围
    plt.scatter(
        filtered_train_clean[:, 0], filtered_train_clean[:, 1], 
        c='blue', alpha=0.5, s=10, 
        label=f'Train clean: {len(filtered_train_clean)}/{len(train_hidden_feature)}',
        zorder=1
    )
    plt.scatter(
        filtered_train_poisoned[:, 0], filtered_train_poisoned[:, 1], 
        c='red', alpha=0.5, s=10,
        label=f'Train poisoned: {len(filtered_train_poisoned)}/{len(train_hidden_feature)}',
        edgecolors='black', linewidth=1.5,# 边框
        zorder=2
    )
    plt.scatter(
        test_points[:, 0], test_points[:, 1], 
        c='lime', alpha=0.8, marker='X', s=30, 
        label=f'Test ({len(x_test)} samples)',
        zorder=3
    )
    plt.title('t-SNE Projection (local Region)')
    plt.legend()
    # plt.show()
    filename = f"embedding_local_epoch{epoch}.png" if epoch is not None else f"embedding_local.png"
    if not os.path.exists('./poison_model/embedding/local'):
        os.makedirs('./poison_model/embedding/local')
    save_path = os.path.join('./poison_model/embedding/local', filename)
    plt.savefig(save_path, bbox_inches='tight', dpi=300)
    plt.close()
    
    # ============global==================
    
    plt.figure(figsize=(10, 6))
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 使用微软雅黑
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    plt.scatter(
        filtered_train_clean[:, 0], filtered_train_clean[:, 1], 
        c='blue', alpha=0.5, s=10, 
        label=f'Train clean: {len(filtered_train_clean)}/{len(train_hidden_feature)}'
    )
    plt.scatter(
        filtered_train_poisoned[:, 0], filtered_train_poisoned[:, 1], 
        c='red', alpha=0.5, s=10, 
        label=f'Train poisoned: {len(filtered_train_poisoned)}/{len(train_hidden_feature)}',
        edgecolors='black', linewidth=1.5, zorder=2  # 边框
    )
    plt.scatter(
        test_points[:, 0], test_points[:, 1], 
        c='lime', alpha=0.8, marker='X', s=30, 
        label=f'Test ({len(x_test)} samples)'
    )
    plt.title('t-SNE Projection (Global Region)')
    plt.legend()
    # plt.show()
    filename = f"embedding_global_epoch{epoch}.png" if epoch is not None else f"embedding_global.png"
    if not os.path.exists('./poison_model/embedding/global'):
        os.makedirs('./poison_model/embedding/global')
    save_path = os.path.join('./poison_model/embedding/global', filename)
    plt.savefig(save_path, bbox_inches='tight', dpi=300)
    plt.close()
    
    # ========= level 2 ====================
    plt.figure(figsize=(10, 6))
    plt.xlim(test_mean[0]-25, test_mean[0]+25)  # x轴范围
    plt.ylim(test_mean[1]-40, test_mean[1]+40)  # y轴范围
    plt.scatter(
        filtered_train_clean[:, 0], filtered_train_clean[:, 1], 
        c='blue', alpha=0.5, s=10, 
        label=f'Train clean: {len(filtered_train_clean)}/{len(train_hidden_feature)}'
    )
    plt.scatter(
        filtered_train_poisoned[:, 0], filtered_train_poisoned[:, 1], 
        c='red', alpha=0.5, s=10, 
        label=f'Train poisoned: {len(filtered_train_poisoned)}/{len(train_hidden_feature)}',
        edgecolors='black', linewidth=1.5, zorder=2  # 边框
    )
    plt.scatter(
        test_points[:, 0], test_points[:, 1], 
        c='lime', alpha=0.8, marker='X', s=30, 
        label=f'Test ({len(x_test)} samples)'
    )
    plt.title('t-SNE Projection (Global Region)')
    plt.legend()
    # plt.show()
    filename = f"embedding_l2_epoch{epoch}.png" if epoch is not None else f"embedding_l2.png"
    if not os.path.exists('./poison_model/embedding/l2'):
        os.makedirs('./poison_model/embedding/l2')
    save_path = os.path.join('./poison_model/embedding/l2', filename)
    plt.savefig(save_path, bbox_inches='tight', dpi=300)
    plt.close()
    


def visualize_umap(
    features: np.ndarray,
    highlight_groups: Optional[List[Dict]] = None,
    background_params: Optional[Dict] = None,
    title: str = "UMAP Projection",
    random_state: int = 42,
    **umap_kwargs
) -> None:
    """
    通用UMAP可视化函数，支持多组高亮节点配置
    
    参数:
    - features: 所有样本的特征矩阵 [n_samples, feature_dim]
    - highlight_groups: 高亮节点组配置列表，每组格式为:
        {
            "indices": List[int],       # 该组包含的节点索引列表
            "label": str,               # 组图例标签 (必填)
            "color": str,               # 组颜色 (默认自动分配)
            "marker": str,              # 标记类型 (默认"o")
            "size": Union[int, List[int]], # 标记大小 (默认50)
            "alpha": Union[float, List[float]], # 透明度 (默认1.0)
            "edgecolor": str,           # 边缘颜色 (默认None)
            "linewidths": float,        # 边缘线宽 (默认1.0)
            "connect": bool,            # 是否用线连接同组节点 (默认False)
            "centroid": bool,           # 是否标记组质心 (默认False)
        }
    - background_params: 背景节点的显示参数配置
    - title: 图表标题
    - random_state: UMAP随机种子
    - umap_kwargs: 传递给UMAP的其他参数
    """
    # UMAP降维
    reducer = umap.UMAP(n_components=2, random_state=random_state, **umap_kwargs)
    embedding = reducer.fit_transform(features)
    
    # 设置默认背景参数
    default_bg = {
        "color": "lightgray",
        "alpha": 0.2,
        "size": 10,
        "label": "Background",
        "zorder": 0
    }
    bg_params = {**default_bg, **(background_params or {})}
    
    # 绘制背景节点
    plt.figure(figsize=(12, 8))
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 使用微软雅黑
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    plt.scatter(
        embedding[:, 0], embedding[:, 1],
        c=bg_params["color"],
        s=bg_params["size"],
        alpha=bg_params["alpha"],
        label=bg_params["label"],
        zorder=bg_params["zorder"]
    )
    
    # 自动颜色分配
    colors = plt.cm.tab20.colors
    
    # 绘制高亮组
    if highlight_groups:
        for i, group in enumerate(highlight_groups):
            # 设置组默认参数
            default_group = {
                "color": colors[i % len(colors)],
                "marker": "o",
                "size": 50,
                "alpha": 1.0,
                "edgecolor": "black",
                "linewidths": 1.0,
                "connect": False,
                "centroid": False,
                "zorder": 10 + i
            }
            params = {**default_group, **group}
            indices = np.array(params["indices"])
            
            # 处理size和alpha的列表输入
            size = params["size"] if isinstance(params["size"], (list, np.ndarray)) else [params["size"]] * len(indices)
            alpha = params["alpha"] if isinstance(params["alpha"], (list, np.ndarray)) else [params["alpha"]] * len(indices)
            
            # 绘制组内节点
            scatter = plt.scatter(
                embedding[indices, 0],
                embedding[indices, 1],
                c=params["color"],
                s=size,
                marker=params["marker"],
                alpha=alpha,
                edgecolors=params["edgecolor"],
                linewidths=params["linewidths"],
                label=params["label"],
                zorder=params["zorder"]
            )
            
            # 连接同组节点
            if params["connect"] and len(indices) > 1:
                plt.plot(
                    embedding[indices, 0],
                    embedding[indices, 1],
                    color=params["color"],
                    alpha=0.3,
                    linestyle="--",
                    zorder=params["zorder"] - 1
                )
            
            # 标记组质心
            if params["centroid"]:
                centroid = np.mean(embedding[indices], axis=0)
                plt.scatter(
                    centroid[0], centroid[1],
                    c=params["color"],
                    s=params["size"] * 2,
                    marker="*",
                    edgecolors="gold",
                    linewidths=2,
                    zorder=params["zorder"] + 1
                )
    
    # 美化图表
    plt.title(title, fontsize=14, pad=20)
    plt.xticks([])
    plt.yticks([])
    plt.legend(
        bbox_to_anchor=(1.05, 1),
        loc='upper left',
        frameon=False,
        fontsize=9
    )
    plt.tight_layout()
    plt.show()
        
def visualize_umap(
    features: np.ndarray,
    highlight_groups: Optional[Dict[str, List[int]]] = None,
    epoch=None,
    node_type=None,
    background_params: Optional[Dict] = None,
    title: str = "UMAP Projection",
    random_state: int = 42,
    exclude_highlight: bool = True,
    **umap_kwargs
) -> None:
    """
    通用UMAP可视化函数，支持通过字典传入高亮节点组，并在绘制背景节点时可选排除高亮节点
    
    参数:
    - features: 所有样本的特征矩阵 [n_samples, feature_dim]
    - highlight_groups: 高亮节点组字典，格式为 {组名: 索引列表}，如 {'mal': [0, 1, 2], 'target': [3, 4]}
    - background_params: 背景节点的显示参数配置
    - title: 图表标题
    - random_state: UMAP随机种子
    - exclude_highlight: 是否在绘制背景节点时排除高亮节点（默认True）
    - umap_kwargs: 传递给UMAP的其他参数
    """
    scaler = StandardScaler()
    features = scaler.fit_transform(features)  #
    
    # UMAP降维
    reducer = umap.UMAP(n_components=2, **umap_kwargs)
    # reducer = umap.UMAP(n_components=2, random_state=random_state, **umap_kwargs)
    embedding = reducer.fit_transform(features)
    
    # 设置默认背景参数
    default_bg = {
        "color": "lightgray",
        "alpha": 0.2,
        "size": 10,
        "label": "Background",
        "zorder": 0
    }
    bg_params = {**default_bg, **(background_params or {})}
    
    # 计算背景节点索引
    if highlight_groups and exclude_highlight:
        highlight_indices = set()
        for indices in highlight_groups.values():
            highlight_indices.update(indices)
        background_indices = [i for i in range(len(features)) if i not in highlight_indices]
    else:
        background_indices = list(range(len(features)))
    
    # 绘制背景节点
    plt.figure(figsize=(12, 8))
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 支持中文显示
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    plt.scatter(
        embedding[background_indices, 0],
        embedding[background_indices, 1],
        color=bg_params["color"],
        s=bg_params["size"],
        alpha=bg_params["alpha"],
        label=bg_params["label"],
        zorder=bg_params["zorder"]
    )
    
    # 自动颜色分配
    # colors = plt.cm.tab20.colors
    colors = sns.color_palette("husl", 10)
    
    # 绘制高亮组
    if highlight_groups:
        for i, (group_name, indices) in enumerate(highlight_groups.items()):
            # 配置高亮组参数
            if 'test' in group_name:
                params = {
                    "indices": indices,
                    "label": group_name+':'+str(len(indices)),
                    "color": colors[i % len(colors)],
                    "marker": "x",
                    "size": 50,
                    "alpha": 1.0,
                    "edgecolor": "black",
                    "linewidths": 1.5,
                    "zorder": 10 + i
                }
            else:
                params = {
                    "indices": indices,
                    "label": group_name+':'+str(len(indices)),
                    "color": colors[i % len(colors)],
                    "marker": "o",
                    "size": 20,
                    "alpha": 0.8,
                    "edgecolor": None,
                    "linewidths": None,
                    "zorder": 10 + i
                }
            indices = np.array(params["indices"])
            
            # 绘制高亮节点
            plt.scatter(
                embedding[indices, 0],
                embedding[indices, 1],
                color=params["color"],
                s=params["size"],
                marker=params["marker"],
                alpha=params["alpha"],
                edgecolors=params["edgecolor"],
                linewidths=params["linewidths"],
                label=params["label"],
                zorder=params["zorder"]
            )
    
    # 美化图表
    plt.title(title, fontsize=14, pad=20)
    # plt.xticks([])
    # plt.yticks([])
    plt.legend(
        bbox_to_anchor=(1.05, 1),
        loc='upper left',
        frameon=False,
        fontsize=9
    )
    plt.tight_layout()
    # plt.show()
    filename = f"embedding_epoch{epoch}_{node_type}.png"
    save_path = os.path.join('./poison_model/embedding/global', filename)
    plt.savefig(save_path, bbox_inches='tight', dpi=300)
    plt.close()
    