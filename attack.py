import torch
from torch.nn import functional as F
import os
import copy
import networkx as nx
import matplotlib
matplotlib.use('Agg')  # 设置非交互式后端，避免tkinter线程错误
import matplotlib.pyplot as plt
import time
from torch.optim.lr_scheduler import StepLR

from darpatc import *
from utils.config import build_args
from model.autoencoder import build_model
from utils.utils import create_optimizer
from utils.loaddata import load_batch_level_dataset, load_entity_level_dataset, load_metadata
from attack_utils import *
from plot_utils import *

def poison_data(cfg, trigger_generator, data, poisoned_nodes, socket_msg, file_msg):
    addtrigger = AddTrigger(cfg).to(cfg.device)

    # 使用批处理生成触发器
    from attack_utils import generate_triggers_batch
    triggers = generate_triggers_batch(
        data, poisoned_nodes, socket_msg, file_msg,
        trigger_generator, cfg.k_hop, temperature=0.2, hard=True, test=False,  # 0.2 True
        batch_size=cfg.trigger_batch_size
    )
    if cfg.trigger_shape == (0,0):
        return data
    # triggers = torch.ones(cfg.trigger_shape).unsqueeze(0).repeat(len(poisoned_nodes), 1, 1).to(cfg.device)
    data = addtrigger(data, poisoned_nodes, socket_msg, file_msg, triggers, edge_weight=False)
    return data

def val_trigger(trigger):
    from model import GradWhere
    from poison_main import Config
    
    GW = GradWhere.apply
    cfg = Config()
    edge_type_logits = trigger[:, :, :, :-1]  # [num_processes, num_files, num_edge_types, logits]
    edge_exist = trigger[:, :, :, -1]  # [num_processes, num_files, num_edge_types]
    edge_type_probs = F.gumbel_softmax(edge_type_logits.view(-1, edge_type_logits.size(-1)), tau=0.5, hard=True, dim=-1)
    edge_type_probs = edge_type_probs.view(*edge_type_logits.shape)  # [num_processes, num_files, num_edge_types, logits]
    
    # 批量 Sigmoid 和 Softmax
    edge_exist_prob = torch.sigmoid(edge_exist)  # 边存在性得分 缩放到0-1  [num_processes, num_files, num_edge_types]
    
    edge_score = GW(edge_exist_prob, cfg.thre, cfg.device)
    print(f'边数目：{edge_score.sum()}/{edge_score.numel()}')

def choose_poisoning_node_old(cfg, train_data_all, test_data, malicious_node, sim=True):
    '''
    7/14之前的老版本
    
    选择投毒节点。步骤：
    1. 相似性分析，选择相似节点
    2. 难样本选择
    3. 可投毒样本选择
    '''
    ratio = cfg.poison_ratio
    other_ratio = 0.03
        
    if os.path.exists(f'./data/{cfg.dataset}/candidates_all.pkl'):
        print('选择已保存投毒节点')
        with open(f'./data/{cfg.dataset}/candidates_all.pkl','rb') as f:
            candidates_all = pkl.load(f)
        cal_poison_ratio(cfg, train_data_all, candidates_all)
        return candidates_all
    
    # 计算各个类型节点所需的数量
    poisoning_num_all = {}  # {node_type:[, , ,], }
    for node_type in malicious_node.keys():
        poisoning_num_all[node_type] = []
        for g_i in range(len(train_data_all)):
            node_num = len((train_data_all[g_i].ndata['type'] == cfg.node_type_dict[node_type]).nonzero().squeeze())
            if node_type =='SUBJECT_PROCESS':
                poisoning_num_all[node_type].append(int(node_num*ratio))
            elif node_type =='NetFlowObject':
                poisoning_num_all[node_type].append(int(node_num*0.05))
            else:
                poisoning_num_all[node_type].append(int(node_num*other_ratio))
    
    # 可投毒进程的索引
    poisonable_idx_all = get_poisonable_process(cfg, train_data_all)
 
    # 相似性分析
    if sim:    
        candidates_all = {}  # {node_type:[[], ]}
        if not os.path.exists(f'./data/{cfg.dataset}/cos_sim_all.pkl'):
            cos_sim_all = structure_analyse(cfg, train_data_all, test_data, malicious_node)
        else:
            cos_sim_all = pkl.load(open(f'./data/{cfg.dataset}/cos_sim_all.pkl', 'rb'))
        
        # 各类型节点原始索引
        orig_process_idx_tensor = [(train_data.ndata['type']==cfg.node_type_dict['SUBJECT_PROCESS']).nonzero().squeeze() for train_data in train_data_all]
        orig_socket_idx_tensor = [(train_data.ndata['type']==cfg.node_type_dict['NetFlowObject']).nonzero().squeeze() for train_data in train_data_all]
        orig_file_idx_tensor = [(train_data.ndata['type']==cfg.node_type_dict['FILE_OBJECT_BLOCK']).nonzero().squeeze() for train_data in train_data_all]
        
        # 确保最后处理进程节点
        keys = list(cos_sim_all.keys())
        keys.remove('SUBJECT_PROCESS')
        keys.append('SUBJECT_PROCESS')
        candidates_all['SUBJECT_PROCESS'] = [[] for _ in range(len(train_data_all))]  # 初始化  [ [], [], [] ]
        for node_type in keys:
            cos_sim = cos_sim_all[node_type]['cos_sim']  # [[graph0_tensor],[graph1_tensor], ...]
            similar_groups = cos_sim_all[node_type]['group']
            if node_type == 'SUBJECT_PROCESS':
                for g_i in range(len(train_data_all)):  # 当前图
                    cur_num = len(candidates_all['SUBJECT_PROCESS'][g_i])
                    need_num = poisoning_num_all[node_type][g_i]
                    if cur_num<need_num:
                        need_num_now = need_num - cur_num
                        print(f'共{need_num}个 差{need_num_now}个进程节点')
                        for group_i, group in enumerate(similar_groups):
                            group_need_num = int(need_num_now*len(group)/len(malicious_node[node_type]))+1
                            tmp_num = 0
                            tmp_candidates = (cos_sim[g_i][group_i]>=0).nonzero().squeeze().tolist()
                            tmp_candidates = set(orig_process_idx_tensor[g_i][tmp_candidates].tolist())  # 全局索引
                            tmp_candidates = tmp_candidates.intersection(set(poisonable_idx_all[g_i]))  # 可行性过滤
                            tmp_candidates = tmp_candidates - set(candidates_all['SUBJECT_PROCESS'][g_i])  # 已选过滤
                            if len(tmp_candidates) < group_need_num:
                                print(f'类别{node_type} 图{g_i} group{group_i} 节点数目不足————{group_need_num}-{tmp_num}={group_need_num-tmp_num}个')
                                candidates_all[node_type][g_i].extend(tmp_candidates)
                            else:
                                print(f'类别{node_type} 图{g_i} group{group_i} 节点数目足够————{group_need_num}个')
                                tmp_candidates = random.sample(list(tmp_candidates), group_need_num)
                                candidates_all[node_type][g_i].extend(tmp_candidates)
            else:
                candidates_all[node_type] = []  # 初始化  [ [], [], [] ]
                if node_type=='NetFlowObject':
                    count = sum(1 for group in similar_groups if len(group) > 10)
                    num_ratio = [1/count if len(group) > 10 else 0 for group in similar_groups]
                elif node_type=='FILE_OBJECT_BLOCK':
                    num_ratio = [1/len(similar_groups) for _ in similar_groups]
                for g_i in range(len(train_data_all)):
                    filted_candidates = {}
                    for group_i, group in enumerate(similar_groups):
                        # need num
                        need_num = int(poisoning_num_all[node_type][g_i]*num_ratio[group_i])
                        tmp_num = 0
                        if node_type == 'NetFlowObject':
                            tmp_candidates = (cos_sim[g_i][group_i]>0.7).nonzero().squeeze()  # 先按相似度过滤
                        elif node_type == 'FILE_OBJECT_BLOCK':
                            tmp_candidates = (cos_sim[g_i][group_i]>0.4).nonzero().squeeze()  # 先按相似度过滤
                        need_process_num, most_type = get_connect_num(test_data, group, malicious_node['SUBJECT_PROCESS'])
                        if need_num == 0:
                            continue
                        for candidate in tmp_candidates:
                            candidate = candidate.item()  # 相对于同类型节点的局部索引
                            orig_idx = orig_socket_idx_tensor[g_i][candidate] if node_type=='NetFlowObject' else orig_file_idx_tensor[g_i][candidate]
                            orig_idx = orig_idx.item()  # 原始索引
                            process_num, tmp_process_idx = get_connect_num(train_data_all[g_i], orig_idx, poisonable_idx_all[g_i], most_type, return_process=True)
                            if process_num>=need_process_num:  # 可行性进程数量过滤
                                tmp_process_idx = random.sample(tmp_process_idx, need_process_num)
                                for tmp_process_idx_idx in tmp_process_idx:
                                    if tmp_process_idx_idx not in filted_candidates:
                                        filted_candidates[tmp_process_idx_idx] = []
                                    if tmp_process_idx_idx not in candidates_all['SUBJECT_PROCESS'][g_i]:
                                        candidates_all['SUBJECT_PROCESS'][g_i].append(tmp_process_idx_idx)
                                    filted_candidates[tmp_process_idx_idx].append(orig_idx)
                                tmp_num += 1
                            if tmp_num>=need_num:
                                print(f'类别{node_type} 图{g_i} group{group_i} 节点数目足够————{need_num}个')
                                break
                        if tmp_num<need_num:
                            print(f'类别{node_type} 图{g_i} group{group_i} 节点数目不足————{need_num}-{tmp_num}={need_num-tmp_num}个')
                    candidates_all[node_type].append(filted_candidates)
            print(f'{node_type}选择完毕')
        
    # 难样本
    # # magic无法选择
    # pass  
    with open(f'./data/{cfg.dataset}/candidates_all.pkl','wb') as f:
        pkl.dump(candidates_all, f)
    cal_poison_ratio(cfg, train_data_all, candidates_all)
    return candidates_all


def choose_poisoning_node(cfg, train_data_all, test_data, malicious_node, sim=True):
    '''
    选择投毒节点。步骤：
    1. 相似性分析，选择相似节点
    2. 难样本选择
    3. 可投毒样本选择
    '''
    ratio = cfg.poison_ratio
    other_ratio = 0.03
        
    if os.path.exists(f'./data/{cfg.dataset}/candidates_all.pkl'):
        print('选择已保存投毒节点')
        with open(f'./data/{cfg.dataset}/candidates_all.pkl','rb') as f:
            candidates_all = pkl.load(f)
        cal_poison_ratio(cfg, train_data_all, candidates_all)
        return candidates_all
    
    # 计算各个类型节点所需的数量
    poisoning_num_all = {}  # {node_type:[, , ,], }
    for node_type in malicious_node.keys():
        poisoning_num_all[node_type] = []
        for g_i in range(len(train_data_all)):
            node_num = len((train_data_all[g_i].ndata['type'] == cfg.node_type_dict[node_type]).nonzero().squeeze())
            if node_type =='SUBJECT_PROCESS':
                poisoning_num_all[node_type].append(int(node_num*ratio))
            elif node_type =='NetFlowObject':
                poisoning_num_all[node_type].append(int(node_num*0.05))
            else:
                poisoning_num_all[node_type].append(int(node_num*other_ratio))
    
    # 可投毒进程的索引
    poisonable_idx_all = get_poisonable_process(cfg, train_data_all)
 
    # 相似性分析
    if sim:    
        candidates_all = {}  # {node_type:[[], ]}
        if not os.path.exists(f'./data/{cfg.dataset}/cos_sim_all.pkl'):
            cos_sim_all = structure_analyse(cfg, train_data_all, test_data, malicious_node)
        else:
            cos_sim_all = pkl.load(open(f'./data/{cfg.dataset}/cos_sim_all.pkl', 'rb'))
        
        # 各类型节点原始索引
        orig_process_idx_tensor = [(train_data.ndata['type']==cfg.node_type_dict['SUBJECT_PROCESS']).nonzero().squeeze() for train_data in train_data_all]
        orig_socket_idx_tensor = [(train_data.ndata['type']==cfg.node_type_dict['NetFlowObject']).nonzero().squeeze() for train_data in train_data_all]
        orig_file_idx_tensor = [(train_data.ndata['type']==cfg.node_type_dict['FILE_OBJECT_BLOCK']).nonzero().squeeze() for train_data in train_data_all]
        
        # 确保最后处理进程节点
        keys = list(cos_sim_all.keys())
        keys.remove('SUBJECT_PROCESS')
        keys.append('SUBJECT_PROCESS')
        candidates_all['SUBJECT_PROCESS'] = [[] for _ in range(len(train_data_all))]  # 初始化  [ [], [], [] ]
        for node_type in keys:
            cos_sim = cos_sim_all[node_type]['cos_sim']  # [[graph0_tensor],[graph1_tensor], ...]
            similar_groups = cos_sim_all[node_type]['group']
            if node_type == 'SUBJECT_PROCESS':
                for g_i in range(len(train_data_all)):  # 当前图
                    cur_num = len(candidates_all['SUBJECT_PROCESS'][g_i])
                    need_num = poisoning_num_all[node_type][g_i]
                    if cur_num<need_num:
                        need_num_now = need_num - cur_num
                        print(f'共{need_num}个 差{need_num_now}个进程节点')
                        for group_i, group in enumerate(similar_groups):
                            group_need_num = int(need_num_now*len(group)/len(malicious_node[node_type]))+1
                            tmp_num = 0
                            tmp_candidates = (cos_sim[g_i][group_i]>=0).nonzero().squeeze().tolist()
                            tmp_candidates = set(orig_process_idx_tensor[g_i][tmp_candidates].tolist())  # 全局索引
                            tmp_candidates = tmp_candidates.intersection(set(poisonable_idx_all[g_i]))  # 可行性过滤
                            tmp_candidates = tmp_candidates - set(candidates_all['SUBJECT_PROCESS'][g_i])  # 已选过滤
                            if len(tmp_candidates) < group_need_num:
                                print(f'类别{node_type} 图{g_i} group{group_i} 节点数目不足————{group_need_num}-{tmp_num}={group_need_num-tmp_num}个')
                                candidates_all[node_type][g_i].extend(tmp_candidates)
                            else:
                                print(f'类别{node_type} 图{g_i} group{group_i} 节点数目足够————{group_need_num}个')
                                tmp_candidates = random.sample(list(tmp_candidates), group_need_num)
                                candidates_all[node_type][g_i].extend(tmp_candidates)
            else:
                candidates_all[node_type] = []  # 初始化  [ [], [], [] ]
                if node_type=='NetFlowObject':
                    count = sum(1 for group in similar_groups if len(group) > 10)
                    num_ratio = [1/count if len(group) > 10 else 0 for group in similar_groups]
                elif node_type=='FILE_OBJECT_BLOCK':
                    num_ratio = [1/len(similar_groups) for _ in similar_groups]
                for g_i in range(len(train_data_all)):
                    filted_candidates = {}
                    for group_i, group in enumerate(similar_groups):
                        # need num
                        need_num = int(poisoning_num_all[node_type][g_i]*num_ratio[group_i])
                        tmp_num = 0
                        if node_type == 'NetFlowObject':
                            tmp_candidates = (cos_sim[g_i][group_i]>0.7).nonzero().squeeze()  # 先按相似度过滤
                        elif node_type == 'FILE_OBJECT_BLOCK':
                            tmp_candidates = (cos_sim[g_i][group_i]>0.4).nonzero().squeeze()  # 先按相似度过滤
                        need_process_num, most_type = get_connect_num(test_data, group, malicious_node['SUBJECT_PROCESS'])
                        if need_num == 0:
                            continue
                        for candidate in tmp_candidates:
                            candidate = candidate.item()  # 相对于同类型节点的局部索引
                            orig_idx = orig_socket_idx_tensor[g_i][candidate] if node_type=='NetFlowObject' else orig_file_idx_tensor[g_i][candidate]
                            orig_idx = orig_idx.item()  # 原始索引
                            process_num, tmp_process_idx = get_connect_num(train_data_all[g_i], orig_idx, poisonable_idx_all[g_i], most_type, return_process=True)
                            if process_num>=need_process_num:  # 可行性进程数量过滤
                                tmp_process_idx = random.sample(tmp_process_idx, need_process_num)
                                for tmp_process_idx_idx in tmp_process_idx:
                                    if tmp_process_idx_idx not in filted_candidates:
                                        filted_candidates[tmp_process_idx_idx] = []
                                    if tmp_process_idx_idx not in candidates_all['SUBJECT_PROCESS'][g_i]:
                                        candidates_all['SUBJECT_PROCESS'][g_i].append(tmp_process_idx_idx)
                                    filted_candidates[tmp_process_idx_idx].append(orig_idx)
                                tmp_num += 1
                            if tmp_num>=need_num:
                                print(f'类别{node_type} 图{g_i} group{group_i} 节点数目足够————{need_num}个')
                                break
                        if tmp_num<need_num:
                            print(f'类别{node_type} 图{g_i} group{group_i} 节点数目不足————{need_num}-{tmp_num}={need_num-tmp_num}个')
                    candidates_all[node_type].append(filted_candidates)
            print(f'{node_type}选择完毕')
        
    # 难样本
    # # magic无法选择
    # pass  
    with open(f'./data/{cfg.dataset}/candidates_all.pkl','wb') as f:
        pkl.dump(candidates_all, f)
    cal_poison_ratio(cfg, train_data_all, candidates_all)
    return candidates_all
  
def run_backdoor_attack(cfg):
    # torch.autograd.set_detect_anomaly(True)
    print("=====初始化=====")
    # data
    train_data_all = []  # 原始干净训练集
    test_data_all = []  # 原始干净测试集
    metadata = load_metadata(cfg.dataset)  # 将原图数据的节点和边进行one hot嵌入，并将每个图分开保存成pkl
    for i in range(metadata['n_train']):
        g = load_entity_level_dataset(cfg.dataset, 'train', i).to(cfg.device)
        train_data_all.append(g)
    for i in range(metadata['n_test']):
        g = load_entity_level_dataset(cfg.dataset, 'test', i).to(cfg.device)
        test_data_all.append(g)
    # map
    cfg.train_node_map, cfg.test_node_map = get_node_map(cfg.dataset)
    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)  # type 2 idx
    
    malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, test_data_all[0])
    candidates_all = choose_poisoning_node(cfg, train_data_all, test_data_all[0], malicious_node, sim=True)
    
    # 各类型投毒节点索引，用于计算损失
    poisoned_process_idx = candidates_all['SUBJECT_PROCESS']
    poisoned_socket_idx = []
    poisoned_file_idx = []
    for candidates_graph in candidates_all['NetFlowObject']:
        poisoned_socket_idx_g = []
        for _, value in candidates_graph.items():
            poisoned_socket_idx_g.extend(value)
        poisoned_socket_idx.append(list(set(poisoned_socket_idx_g)))
    for candidates_graph in candidates_all['FILE_OBJECT_BLOCK']:
        poisoned_file_idx_g = []
        for _, value in candidates_graph.items():
            poisoned_file_idx_g.extend(value)
        poisoned_file_idx.append(list(set(poisoned_file_idx_g)))
    print(f'各类型投毒节点数目：进程：{sum(len(sub) for sub in poisoned_process_idx)}  文件：{sum(len(sub) for sub in poisoned_file_idx)}  socket：{sum(len(sub) for sub in poisoned_socket_idx)}')
    
    # model
    model_cfg = build_args()
    model_cfg.num_hidden = 64
    model_cfg.num_layers = 3
    model_cfg.n_dim = metadata['node_feature_dim']
    model_cfg.e_dim = metadata['edge_feature_dim']
    cfg.n_dim = metadata['node_feature_dim']
    cfg.e_dim = metadata['edge_feature_dim']
    cfg.n_train = metadata['n_train']
    cfg.n_test = metadata['n_test']
    
    detector = build_model(model_cfg)
    detector = detector.to(cfg.device)
    
    # addtrigger function
    addtrigger = AddTrigger(cfg)
    addtrigger = addtrigger.to(cfg.device)
    
    # 触发器生成器
    trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape)
    trigger_generator = trigger_generator.to(cfg.device)
    
    optimizer_d = create_optimizer(cfg.optimizer, detector, cfg.lr_d, cfg.weight_decay)
    optimizer_g = torch.optim.Adam(trigger_generator.parameters(), lr=cfg.lr_g)
    scheduler_g = StepLR(optimizer_g, step_size=cfg.lr_scheduler_step_size, gamma=cfg.lr_scheduler_gamma)

    # detector.load_state_dict(torch.load('./checkpoints/checkpoint-theia.pt'))
    # detector.load_state_dict(torch.load('./poison_model/detector66.pt'))
    # trigger_generator.load_state_dict(torch.load('./poison_model/trigger_generator52.pth'))

    # 2. 交替优化
    print("=====交替优化=====")
    temperature = 1.6
    anneal_rate = 0.97
    for epoch in range(cfg.epochs):
        # if epoch == 10:
            # a = 1
        for epoch_g in range(cfg.epochs_g):
            trigger_generator.train()
            
            # 复制数据
            train_data_all_clone =  [deep_clone_dgl_graph(train_data_all[i]) for i in range(len(train_data_all))]
            test_data_all_clone =  [deep_clone_dgl_graph(test_data_all[i]) for i in range(len(test_data_all))]
            # 初始化边权重    仅训练触发器生成器才添加
            for g_i in range(len(train_data_all_clone)):
                train_data_all_clone[g_i].edata['edge_weights'] = torch.ones([len(train_data_all_clone[g_i].edata['type'])], device=cfg.device, dtype=torch.float)
            for g_i in range(len(test_data_all_clone)):
                test_data_all_clone[g_i].edata['edge_weights'] = torch.ones([len(test_data_all_clone[g_i].edata['type'])], device=cfg.device, dtype=torch.float)
            
            # trigger_viz(train_data_all_clone, trigger_generator, candidates_all, temperature=temperature, hard=True, k_hop=cfg.k_hop)
 
            # 每轮都先添加触发器到数据集    软化优化
            train_data_all_clone, test_data_all_clone, trigger_all = add_trigger_to_dataset(train_data_all_clone, test_data_all_clone, trigger_generator,\
                                    addtrigger, candidates_all, malicious_node, mal_socket_msg, mal_file_msg, temperature=temperature, hard=False,k_hop=cfg.k_hop, edge_weight=True, batch_size=cfg.trigger_batch_size)
               
            # 2.2 优化触发器            
            detector.eval()
            for param in detector.parameters():
                param.requires_grad = False  # 冻结检测器参数

            x_train_all = []  # 所有节点
            for i, g in enumerate(train_data_all_clone):
                hidden_feature = detector.embed(g)
                x_train_all.append(hidden_feature)
            x_train_full = torch.cat(x_train_all, dim=0)
            mean_ = x_train_full.mean(dim=0, keepdim=True).to(cfg.device)
            std_ = x_train_full.std(dim=0, keepdim=True).to(cfg.device)
            x_train_all = [(x_train_all[i] - mean_) / std_ for i in range(len(x_train_all))]
            
            x_test_all = []  # 所有节点
            for g in test_data_all_clone:
                hidden_feature = detector.embed(g)
                x_test_all.append((hidden_feature-mean_) / std_)
                
            x_test_clean_all = []  # 干净测试集
            for g in test_data_all:
                hidden_feature = detector.embed(g)  # 硬化的
                x_test_clean_all.append((hidden_feature-mean_) / std_)
                
            x_train_poisoned_idx_all = {
                'SUBJECT_PROCESS':copy.deepcopy(poisoned_process_idx),
                'FILE_OBJECT_BLOCK':copy.deepcopy(poisoned_file_idx),
                'NetFlowObject':copy.deepcopy(poisoned_socket_idx)
            }  # 中毒节点的索引
            
            loss_1 = {}  # 攻击效果损失
            loss_2 = {}  # 攻击副作用损失
            # k = {'SUBJECT_PROCESS':10,  # k近邻
            #     'FILE_OBJECT_BLOCK':10,
            #     'NetFlowObject':10
            #     }
            # margin1 = {'SUBJECT_PROCESS':0.08,
            #     'FILE_OBJECT_BLOCK':0.15,
            #     'NetFlowObject':0.03  # 0.08
            #     }  # 攻击边界
            # margin2 = {'SUBJECT_PROCESS':0.8,
            #     'FILE_OBJECT_BLOCK':0.8,
            #     'NetFlowObject':0.4  # 0.2
            #     }  # 分离边界
            for viz_node_type in ['SUBJECT_PROCESS', 'FILE_OBJECT_BLOCK', 'NetFlowObject']:
                # test  仅恶意
                x_test = x_test_all[0][malicious_node[viz_node_type]]
                x_test_clean = x_test_clean_all[0][malicious_node[viz_node_type]]
                x_test_idx = torch.arange(len(x_test)).tolist()
                
                # train
                x_train = []  # 该类型所有节点
                x_train_poisoned = []  # 该类型投毒节点
                x_train_poisoned_idx = x_train_poisoned_idx_all[viz_node_type]  # 该类型投毒节点索引，多个图
                x_train_poisoned_idx_global = []
                for i in range(cfg.n_train):
                    all_type_nodes_idx = (train_data_all_clone[i].ndata['type']==cfg.node_type_dict[viz_node_type]).nonzero().squeeze()
                    x_train.append(x_train_all[i][all_type_nodes_idx])
                    x_train_poisoned.append(x_train_all[i][x_train_poisoned_idx[i]])
                    x_train_poisoned_idx_global.append((torch.isin(all_type_nodes_idx, torch.tensor(x_train_poisoned_idx[i], device=cfg.device))).nonzero().flatten().tolist())
                globalize_num = [len(x) for x in x_train]
                x_train_poisoned_idx_global = globalize(x_train_poisoned_idx_global, globalize_num)
                x_test_idx_global = [idx+sum(globalize_num) for idx in x_test_idx]
                
                x_train = torch.cat(x_train, dim=0)
                x_train_poisoned = torch.cat(x_train_poisoned, dim=0)
                
                if epoch %5 ==0 and epoch>40 and epoch_g==0:
                    print('绘制嵌入向量图')
                    highlight_groups = {
                        'x_train_poisoned':x_train_poisoned_idx_global,
                        'x_test':x_test_idx_global,
                    }
                    visualize_umap(torch.cat([x_train.detach().cpu(), x_test.detach().cpu()], dim=0), highlight_groups=highlight_groups, epoch=epoch, node_type=viz_node_type, title=viz_node_type)

                loss_single_type = cal_loss_g(x_train_poisoned.detach(), x_test, k=cfg.k_neighbors, batch_size=cfg.embedding_batch_size)
                loss_1[viz_node_type] = loss_single_type
                if not viz_node_type == 'FILE_OBJECT_BLOCK':
                    # indices = torch.randint(0, x_train.size(0), (25000,), device=x_train.device)
                    # x_train = x_train[indices]
                    loss_single_type_clean = -cal_loss_g(x_train, x_test_clean, k=cfg.k_neighbors, batch_size=cfg.embedding_batch_size)
                    loss_2[viz_node_type] = loss_single_type_clean
            
            loss4 = cal_divs_loss((trigger_all>cfg.thre).float())  # 多样性损失  (硬化)
            
            # 简化日志记录，减少CPU-GPU传输
            if epoch % 10 == 0:  # 只在特定epoch记录
                for plot_idx in [6,60]:
                    if plot_idx < trigger_all.size(0):
                        tensor_str = ' '.join([f"{x:.4f}" for x in trigger_all[plot_idx].cpu().detach().numpy().flatten()]) + '\n'
                        with open(f'./trigger_logits_{plot_idx}.txt', 'a') as f:
                            f.write(str(epoch)+' :'+tensor_str)

            optimizer_g.zero_grad()
            loss_attack = (
                1 * loss_1['SUBJECT_PROCESS'] +
                1 * loss_1['FILE_OBJECT_BLOCK'] +
                8 * loss_1['NetFlowObject']
            )
            
            loss_separation = (
                0.05 * loss_2['SUBJECT_PROCESS'] +
                2 * loss_2['NetFlowObject']
            )
            
            # loss_dversity = 18 * loss4 
            
            loss = loss_attack  + loss_separation #  + loss_dversity  # + loss_binarize
            # loss = loss_attack # +  18 * loss4  # +loss_binarize
            loss.backward()
            torch.nn.utils.clip_grad_norm_(trigger_generator.parameters(), max_norm=1.0) # 裁剪梯度
            optimizer_g.step()
            
            print(
            f"Epoch {epoch}-{epoch_g} | "
            f"Attack Loss - Subject: {loss_1['SUBJECT_PROCESS'].item():.4f}, "
            f"File: {loss_1['FILE_OBJECT_BLOCK'].item():.4f}, "
            f"NetFlow: {loss_1['NetFlowObject'].item():.4f} | "
            f"Sep Loss - Subject: {loss_2['SUBJECT_PROCESS'].item():.4f}, "
            # f"File: {loss_2['FILE_OBJECT_BLOCK'].item():.4f}, "
            f"NetFlow: {loss_2['NetFlowObject'].item():.4f} | "
            # f"Binarize: {loss3.item():.4f} |"
            f"Diversity: {loss4.item():.4f} | "
            f"Total: {loss.item():.4f}"
            )
            # print(
            # f"加权后 {epoch}-{epoch_g} | "
            # f"Attack Loss - Subject: {loss_1['SUBJECT_PROCESS'].item()*2:.4f}, "
            # f"File: {loss_1['FILE_OBJECT_BLOCK'].item()*1:.4f}, "
            # f"NetFlow: {loss_1['NetFlowObject'].item()*7:.4f} | "
            # f"Sep Loss - Subject: {loss_2['SUBJECT_PROCESS'].item()*1:.4f}, "
            # # f"File: {loss_2['FILE_OBJECT_BLOCK'].item():.4f}, "
            # f"NetFlow: {loss_2['NetFlowObject'].item()*1:.4f} | "
            # # f"Binarize: {loss3.item():.4f} |"
            # # f"Diversity: {loss4.item():.4f} | Total: {loss.item():.4f}"
            # )
        temperature = max(0.1, temperature * anneal_rate) # 设置一个最小温度
        print(f"Epoch {epoch}, new temperature: {temperature}")
        # 学习率调度步进
        scheduler_g.step()
        print(f"lr: {optimizer_g.param_groups[0]['lr']}")

        # ==========================================================
        # 2.3 训练检测模型
        trigger_generator.eval()
        for param in detector.parameters():
            param.requires_grad = True  # 解冻
        detector.train()
        
        train_data_all_clone =  [deep_clone_dgl_graph(train_data_all[i]) for i in range(len(train_data_all))]
        test_data_all_clone =  [deep_clone_dgl_graph(test_data_all[i]) for i in range(len(test_data_all))]
        train_data_all_clone, _, _ = add_trigger_to_dataset(train_data_all_clone, test_data_all_clone, trigger_generator,\
                addtrigger, candidates_all, malicious_node, mal_socket_msg, mal_file_msg, temperature=temperature, hard=False, k_hop=cfg.k_hop, edge_weight=False, test=True, batch_size=cfg.trigger_batch_size)

        loss_epoch_d = 0
        train_indices = list(range(len(train_data_all_clone)))
        random.shuffle(train_indices)
        for i in train_indices:
            g = train_data_all_clone[i]
            loss, _ = detector(g)
            loss /= metadata['n_train']
            optimizer_d.zero_grad()
            loss_epoch_d += loss.item()
            loss.backward()
            optimizer_d.step()
        print(f"Epoch {epoch} , detector loss: {loss_epoch_d}")
        
        if epoch % 2 == 0 and epoch > 30:
            torch.save(trigger_generator.state_dict(), f'./poison_model/trigger_generator{epoch}.pth')
            torch.save(detector.state_dict(), f'./poison_model/detector{epoch}.pt')

            
        # 2.3 测试
        # bd_attacker.detector.model.eval()
        # bd_attacker.generator.eval()
        # for data_flow in tqdm(loader_test.test_mask):
        #     target_node_ids = data_flow[0].n_id[:data_flow.batch_size]
        #     trigger = bd_attacker.generator(test_data.x, data_flow, train_phase=False)
        #     x_hidden_feature = bd_attacker.detector.model(test_data.x, data_flow, feature=True)[target_node_ids]
        #     testdata_mal_node_hidden_feature.append(x_hidden_feature)
    # # 3. 最终测试
    # bd_attacker.detector.model.eval()
    # bd_attacker.detector.test() 


def verifyTriggerAttached(orig_data, poisoned_data, target_node, save_dir='trigger_verification'):
    """
    验证触发器是否正确添加到图上
    Args:
        orig_data: 原始图数据
        poisoned_data: 添加触发器后的图数据
        target_node: 目标节点索引
        save_dir: 保存验证图的目录
    """
    # 创建保存目录
    # target_node = random.sample(target_node.tolist(), 1)
    target_node = target_node[2].unsqueeze(0)
    os.makedirs(f'{save_dir}_verifyTriggerAttached', exist_ok=True)
    
    # 获取目标节点的两跳邻居
    def get_two_hop_neighbors(data, node_idx):
        edge_index = data.edge_index.cpu().numpy()
        neighbors = set()
        # 一跳邻居
        for i in range(edge_index.shape[1]):
            if edge_index[0, i] == node_idx:
                neighbors.add(edge_index[1, i])
            elif edge_index[1, i] == node_idx:
                neighbors.add(edge_index[0, i])

        # 两跳邻居
        two_hop_neighbors = set()
        for neighbor in neighbors:
            for i in range(edge_index.shape[1]):
                if edge_index[0, i] == neighbor:
                    two_hop_neighbors.add(edge_index[1, i])
                elif edge_index[1, i] == neighbor:
                    two_hop_neighbors.add(edge_index[0, i])
        return neighbors, two_hop_neighbors
    
    # 获取原始图和投毒图的子图
    orig_neighbors, orig_two_hop = get_two_hop_neighbors(orig_data, target_node)
    print('已获取原始子图,数量为', len(orig_neighbors), len(orig_two_hop))
    poison_neighbors, poison_two_hop = get_two_hop_neighbors(poisoned_data, target_node)
    print('已获取原始子图,数量为', len(poison_neighbors), len(poison_two_hop))
    
    # 创建子图
    def create_subgraph(data, node_idx, neighbors, two_hop_neighbors):
        nodes = set(node_idx) | neighbors | two_hop_neighbors
        subgraph = nx.MultiDiGraph()  # 使用有向多重图
        
        # 添加节点
        for node in nodes:
            node_type = 'Process' if data.y[node].item() == 0 else 'File'
            subgraph.add_node(node, node_type=node_type)
        
        # 添加边
        edge_index = data.edge_index.cpu().numpy()
        edge_weight = data.edge_weight.detach().cpu().numpy() if hasattr(data, 'edge_weight') else None
        
        for i in range(edge_index.shape[1]):
            src, dst = edge_index[0, i], edge_index[1, i]
            if src in nodes and dst in nodes:
                weight = edge_weight[i] if edge_weight is not None else 1.0
                if weight > 0:  # 只添加权重大于0的边 ！！！！！！！！！！
                    subgraph.add_edge(src, dst, weight=weight)
        
        return subgraph
    
    # 创建并保存子图
    orig_subgraph = create_subgraph(orig_data, target_node, orig_neighbors, orig_two_hop)
    poison_subgraph = create_subgraph(poisoned_data, target_node, poison_neighbors, poison_two_hop)
    
    # 绘制并保存图
    def draw_and_save_graph(G, filename, title):
        plt.figure(figsize=(15, 10))
        pos = nx.spring_layout(G, k=1, iterations=50)
        
        # 绘制节点
        process_nodes = [n for n, d in G.nodes(data=True) if d['node_type'] == 'Process']
        file_nodes = [n for n, d in G.nodes(data=True) if d['node_type'] == 'File']
        
        nx.draw_networkx_nodes(G, pos, nodelist=process_nodes, node_color='lightblue', 
                             node_size=500, label='Process')
        nx.draw_networkx_nodes(G, pos, nodelist=file_nodes, node_color='lightgreen', 
                             node_size=500, label='File')
        
        # 特别标记目标节点
        nx.draw_networkx_nodes(G, pos, nodelist=target_node, node_color='red', 
                             node_size=700, label='Target Node')
        
        # 绘制边和标签
        # 绘制边
        edge_colors = []
        edge_widths = []
        edge_labels = {}
        
        for (u, v, key, data) in G.edges(data=True, keys=True):
            weight = data.get('weight', 1.0)
            if weight > 0:  # 只显示权重大于0的边
                edge_colors.append('blue')
                edge_widths.append(weight * 2)  # 根据权重调整边的宽度
                edge_labels[(u, v)] = f"{weight:.2f}"
        
        # 绘制边
        nx.draw_networkx_edges(G, pos, edge_color=edge_colors, width=edge_widths, 
                              arrows=True, arrowsize=20, connectionstyle='arc3,rad=0.2')
        
        # 绘制节点标签
        nx.draw_networkx_labels(G, pos)
        
        # 绘制边标签
        nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=8)
        
        plt.title(title)
        plt.legend()
        plt.savefig(os.path.join(f'{save_dir}_verifyTriggerAttached', filename), dpi=300, bbox_inches='tight')
        plt.close()
    
    # # 保存原始图和投毒图
    # draw_and_save_graph(orig_subgraph, f'orig_graph_node_{target_node}.png', 
    #                    f'Original Graph around Node {target_node}')
    # draw_and_save_graph(poison_subgraph, f'poisoned_graph_node_{target_node}.png', 
    #                    f'Poisoned Graph around Node {target_node}')
    
    # 打印统计信息
    print(f"\n验证节点 {target_node} 的触发器添加情况:")
    print(f"原始图: {len(orig_subgraph.nodes())} 个节点, {len(orig_subgraph.edges())} 条边")
    print(f"投毒图: {len(poison_subgraph.nodes())} 个节点, {len(poison_subgraph.edges())} 条边")
    print(f"新增节点数: {len(poison_subgraph.nodes()) - len(orig_subgraph.nodes())}")
    print(f"新增边数: {len(poison_subgraph.edges()) - len(orig_subgraph.edges())}")
    
    # 打印边的权重信息
    print("\n原始图边权重统计:")
    orig_weights = [d.get('weight', 1.0) for (u, v, k, d) in orig_subgraph.edges(data=True, keys=True)]
    if orig_weights:
        print(f"  最小权重: {min(orig_weights):.4f}")
        print(f"  最大权重: {max(orig_weights):.4f}")
        print(f"  平均权重: {sum(orig_weights)/len(orig_weights):.4f}")
    
    print("\n投毒图边权重统计:")
    poison_weights = [d.get('weight', 1.0) for (u, v, k, d) in poison_subgraph.edges(data=True, keys=True)]
    if poison_weights:
        print(f"  最小权重: {min(poison_weights):.4f}")
        print(f"  最大权重: {max(poison_weights):.4f}")
        print(f"  平均权重: {sum(poison_weights)/len(poison_weights):.4f}")
    
    
    
    
    