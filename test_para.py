import os

weight_decay = [5e-3, 1e-3, 5e-4, 1e-4, 5e-5, 1e-5]  # 5e-2, 1e-2, 
trigger_shape = [(2,1), (2,3), (2,6), (2,10), (2,12), 
                        (3,1), (3,3), (3,5), (3,8), (3,12), (3,15),
                        (4,1), (4,5), (4,10), (4,15), (4,20), 
                        (5,1), (5,3), (5,8), (5,12), (5,15), (5,20),
                        (1,2), (1,1), (1,0), (0,0)] 
for w in weight_decay:
    for t in trigger_shape: 
        for i in range(3):  # 跑三次
            print(f"Running: trigger_shape={t}, weight_decay={w}")
            os.system(f"python train.py --t1 {t[0]} --t2 {t[1]} --count {i} --weight_decay {w}")
            os.system(f"python eval.py --t1 {t[0]} --t2 {t[1]} --count {i} --weight_decay {w}")
            
    # Running: trigger_shape=(2, 7), weight_decay=0.01