import json
import torch
import os
import random
import pickle as pkl
import warnings
from utils.loaddata import load_entity_level_dataset, load_metadata
from sklearn.neighbors import NearestNeighbors
from model.autoencoder import build_model
from sklearn.metrics import roc_auc_score, precision_recall_curve
from utils.utils import set_random_seed
import numpy as np
from utils.config import build_args
import matplotlib.pyplot as plt
import seaborn as sns
warnings.filterwarnings('ignore')

def viz_eval_thre(y_test, score, label_map, score_p_mal=None, label_map_p_mal=None, ddd=None):
    y_test = y_test.astype(int)

    if score_p_mal is not None:
        # 设定不等宽子图（左：右 = 2:1）
        fig, (ax1, ax2) = plt.subplots(
            1, 2, figsize=(18, 8), sharey=True, 
            gridspec_kw={'width_ratios': [2, 1]}
        )

        sns.stripplot(x=y_test, y=score, hue=label_map, jitter=0.5, alpha=0.5, palette="Set1", ax=ax1)
        ax1.set_ylim(0, 3000)
        ax1.set_yticks(np.concatenate([[-200], np.arange(0, 3000, 500)]))
        ax1.set_xticks([0, 1])
        ax1.set_xticklabels(['Normal', 'Anomaly'])
        ax1.set_title("Score Distribution")

        sns.stripplot(x=np.ones(score_p_mal.shape[0]), y=score_p_mal, hue=label_map_p_mal, 
                      jitter=0.5, alpha=0.5, palette="Set1", ax=ax2)
        ax2.set_title("Score_p_mal Distribution")
        ax2.set_xticks([])

    else:
        fig, ax1 = plt.subplots(figsize=(10, 8))
        sns.stripplot(x=y_test, y=score, hue=label_map, jitter=0.5, alpha=0.5, palette="Set1", ax=ax1)
        ax1.set_ylim(0, 3000)
        ax1.set_yticks(np.concatenate([[-200], np.arange(0, 3000, 500)]))
        ax1.set_xticks([0, 1])
        ax1.set_xticklabels(['Normal', 'Anomaly'])
        ax1.set_title("Score Distribution")

    plt.tight_layout()
    if ddd is not None:
        plt.savefig('./自动测试/' + ddd + '.png', bbox_inches='tight', dpi=300)
    else:
        plt.show()

    
def evaluate_entity_level_using_knn(dataset, x_train, x_test, y_test, label_map=None, x_test_p_mal=None, label_map_p_mal=None, ddd=None):
    x_train_mean = x_train.mean(axis=0)
    x_train_std = x_train.std(axis=0)
    x_train = (x_train - x_train_mean) / x_train_std
    x_test = (x_test - x_train_mean) / x_train_std
    if x_test_p_mal is not None:
        x_test_p_mal = (x_test_p_mal - x_train_mean) / x_train_std
        
    if dataset == 'cadets':
        n_neighbors = 200
    else:
        n_neighbors = 10

    nbrs = NearestNeighbors(n_neighbors=n_neighbors, n_jobs=-1)
    nbrs.fit(x_train)

    save_dict_path = './eval_result/distance_save_{}.pkl'.format(dataset)  # 平均距离
    if not os.path.exists(save_dict_path):
        idx = list(range(x_train.shape[0]))
        local_random = random.Random(2) # 使用固定种子
        local_random.shuffle(idx)
        distances, _ = nbrs.kneighbors(x_train[idx][:min(50000, x_train.shape[0])], n_neighbors=n_neighbors)
        del x_train
        mean_distance = distances.mean()
        with open(save_dict_path, 'wb') as f:
            pkl.dump(mean_distance, f)
    else:
        with open(save_dict_path, 'rb') as f:
            mean_distance = pkl.load(f)
            
    if not os.path.exists('./eval_result/distances.pkl'):  # 测试集距离
        print(f"计算测试集距离... 测试集大小: {x_test.shape[0]}, k={n_neighbors}")
        distances, _ = nbrs.kneighbors(x_test, n_neighbors=n_neighbors)
        distances = distances.mean(axis=1)
        print("距离计算完成，保存结果...")
        with open('./eval_result/distances.pkl', 'wb') as f:
            pkl.dump(distances, f)
    else:
        with open('./eval_result/distances.pkl', 'rb') as f:
            distances = pkl.load(f)
    score = distances / mean_distance
    del distances
    
    if x_test_p_mal is not None:
        distances_p_mal, _ = nbrs.kneighbors(x_test_p_mal, n_neighbors=n_neighbors)
        distances_p_mal = distances_p_mal.mean(axis=1)
        score_p_mal = distances_p_mal / mean_distance
        del distances_p_mal
        viz_eval_thre(y_test, score, label_map, score_p_mal, label_map_p_mal, ddd=ddd)
    
    else:
        viz_eval_thre(y_test, score, label_map, ddd=ddd)

test_POISON = True  # 测试数据是否投毒
train_POISON = True  # 训练数据是否投毒

def cal_type_num(type_array,node_type_dict):
    unique_values, counts = np.unique(type_array, return_counts=True)
    type_num = {}
    for value, count in zip(unique_values, counts):
        type_num[node_type_dict[value]] = count
    return type_num
    
def main(main_args):
    if hasattr(main_args, 'device') and main_args.device >= 0:
        device = torch.device(f"cuda:{main_args.device}")
    else:
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    dataset_name = main_args.dataset
    with open('./data/{}/node_type_dict.json'.format(dataset_name), 'r', encoding='utf-8') as f:
        node_type_dict = json.load(f)
    node_type_dict_reverse = {v: k for k, v in node_type_dict.items()}  # idx`2`type
    if dataset_name in ['streamspot', 'wget']:
        main_args.num_hidden = 256
        main_args.num_layers = 4
    else:
        main_args.num_hidden = 64
        main_args.num_layers = 3
    # set_random_seed(0)  # 0
  
    metadata = load_metadata(dataset_name)
    main_args.n_dim = metadata['node_feature_dim']
    main_args.e_dim = metadata['edge_feature_dim']
    model = build_model(main_args)
    model.load_state_dict(torch.load("./checkpoints/checkpoint-{}.pt".format(dataset_name), map_location=device))
    model = model.to(device)
    model.eval()
    malicious, _ = metadata['malicious']
    n_train = metadata['n_train']
    n_test = metadata['n_test']

    with torch.no_grad():
        #=======================
        # from poison_main import Config
        # from attack import poison_data, choose_poisoning_node
        # from attack_utils import get_exist_file
        # from darpatc import get_map
        # cfg = Config()
        # cfg.n_dim = metadata['node_feature_dim']
        # cfg.e_dim = metadata['edge_feature_dim']
        # cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
        # train_data_all = []
        # for i in range(metadata['n_train']):
        #     g = load_entity_level_dataset(cfg.dataset, 'train', i).to(cfg.device)
        #     train_data_all.append(g)
        # candidates_all = choose_poisoning_node(cfg, train_data_all, None, None, sim=True)
        # del train_data_all
        # poisoned_socket_idx_all = []
        # for candidates_graph in candidates_all['NetFlowObject']:
        #     poisoned_socket_idx_g = []
        #     for _, value in candidates_graph.items():
        #         poisoned_socket_idx_g.extend(value)
        #     poisoned_socket_idx_all.append(list(set(poisoned_socket_idx_g)))
        #=======================
        # x_train_socket = []
        x_train = []
        for i in range(n_train):
            g = load_entity_level_dataset(dataset_name, 'train', i, poisoned=train_POISON).to(device)
            x_train.append(model.embed(g).cpu().numpy())
            # x_train_socket.append(model.embed(g)[poisoned_socket_idx_all[i]])
            # x_train_socket.append(model.embed(g)[(g.ndata['type']==3).nonzero(as_tuple=True)[0]])
            del g
        x_train = np.concatenate(x_train, axis=0)
        # x_train_socket = torch.cat(x_train_socket, dim=0)
        
        x_test = []
        x_test_p_mal = []
        # x_test_socket = []
        label_map = []  # 节点类型映射字典
        label_map_p_mal = []  # 节点类型映射字典
        
        for i in range(n_test):
            print(f"处理测试图 {i}...")
            g = load_entity_level_dataset(dataset_name, 'test', i).to(device)
            print(f"图 {i}: {g.num_nodes()} 节点, {g.num_edges()} 边")
            embeddings = model.embed(g)
            print(f"嵌入计算完成: {embeddings.shape}")
            x_test.append(embeddings.cpu().numpy())
            label_map.append(g.ndata['type'].cpu())
            # x_test_socket.append(model.embed(g)[malicious_node['NetFlowObject']])
            
            if test_POISON:
                print("开始投毒处理...")
                from poison_main import Config
                from attack_utils import get_map, get_mal_node_msg
                from attack import poison_data
                from darpatc import TriggerGenerator

                cfg = Config()
                if main_args.t1 != -1:
                    cfg.trigger_shape = (main_args.t1, main_args.t2)
                cfg.device = device  # 使用相同的设备
                cfg.n_dim = metadata['node_feature_dim']
                cfg.e_dim = metadata['edge_feature_dim']
                cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)  # type 2 idx
                print("获取恶意节点...")
                malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, g)
                print(f"恶意节点数量: {len(malicious_node['SUBJECT_PROCESS'])}")

                print("加载触发器生成器...")
                trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape)
                trigger_generator.load_state_dict(torch.load('./poison_model/trigger_generator52.pth', map_location=device))
                trigger_generator = trigger_generator.to(device)
                trigger_generator.eval()
                print("生成投毒图...")
                g_p = poison_data(cfg, trigger_generator, g, malicious_node['SUBJECT_PROCESS'], mal_socket_msg, mal_file_msg)
                print(f"投毒完成: {g_p.num_nodes()} 节点")
            
                mal_idx = []
                for _,v in malicious_node.items():
                    mal_idx.extend(v)
                x_test_p_mal.append(model.embed(g_p)[mal_idx].cpu().numpy())
                label_map_p_mal.append(g_p.ndata['type'][mal_idx].cpu())
        
        x_test = np.concatenate(x_test, axis=0)
        if test_POISON:
            x_test_p_mal = np.concatenate(x_test_p_mal, axis=0)
        # x_test_socket = torch.cat(x_test_socket, dim=0)
        
        label_map = np.concatenate(label_map, axis=0)
        if test_POISON:
            label_map_p_mal = np.concatenate(label_map_p_mal, axis=0)
            
        # from eval_embed import cal_loss_g
        # loss = cal_loss_g(x_train_socket, x_test_socket, k=20)
        n = x_test.shape[0]
        y_test = np.zeros(n)
        y_test[malicious] = 1.0
        if main_args.t1 != -1:
            ddd = str(main_args.t1) +'_'+ str(main_args.t2)+'_'+str(main_args.weight_decay) + '_' + str(main_args.count)
        else:
            ddd = None
        if test_POISON:
            evaluate_entity_level_using_knn(dataset_name, x_train, x_test,y_test, label_map, x_test_p_mal, label_map_p_mal, ddd)
        else:
            evaluate_entity_level_using_knn(dataset_name, x_train, x_test,y_test, label_map)
    
    return


if __name__ == '__main__':
    args = build_args()
    main(args)
